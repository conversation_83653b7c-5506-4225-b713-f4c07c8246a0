<template>
    <div class="app-container">
        <div class="store_header">
            <div class="store_header_title">
                <div>{{ name }}</div>
            </div>
        </div>
        <div class="store_content" style="padding: 0;">
            <div class="store_content_header" style="padding: 20px 30px 0 30px;">
                <div class="title_line"></div>
                <div class="title">选择考核商户</div>
            </div>
            <div class="from_header" style="padding: 20px 30px;">
                <el-form :model="queryParams" ref="queryRef" :inline="true">
                    <el-form-item label="地区" prop="deptId">
                        <el-tree-select v-model="queryParams.deptId" :data="deptOptions" style="width: 180px;"
                            :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id"
                            placeholder="请选择地区" check-strictly />
                    </el-form-item>
                    <el-form-item label="诚信等级" prop="chengxindengji">
                        <el-select v-model="queryParams.chengxindengji" placeholder="请选择诚信等级" style="width: 160px">
                            <el-option v-for="item in merchant_cxdj" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="终端类型" prop="zhongduancengji">
                        <el-select v-model="queryParams.zhongduancengji" placeholder="请选择终端类型" style="width: 160px">
                            <el-option v-for="item in merchant_zdlx" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="经营规模" prop="jingyingguimo">
                        <el-select v-model="queryParams.jingyingguimo" placeholder="请选择经营规模" style="width: 160px">
                            <el-option v-for="item in merchant_jygm" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="抽取数量" prop="num">
                        <el-input-number v-model="queryParams.num" :min="0"
                            :max="Math.max(0, inspectedHouseholds - newMerchantsByConditions.length)"
                            style="width: 160px" :controls="false" placeholder="请输入抽取数量" />
                    </el-form-item>
                    <el-form-item>
                        <el-button class="btn-search" @click="handleQuery">立即抽取</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="merchants">
                <div class="merchants_left">
                    <div class="merchants_left_header">
                        <div class="header__left">
                            <div class="store_content_header">
                                <div class="title_line"></div>
                                <div class="title">考核商户</div>
                            </div>
                            <el-form-item label="地区" prop="countyId" style="margin-left: 20px;margin-bottom: 0;">
                                <el-select v-model="deptId" placeholder="请选择所属地区" @change="chooseCounty" clearable>
                                    <el-option v-for="item in deptOptionsList" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </div>
                        <el-button class="btn-delete" icon="Delete" @click="deleteAll">删除</el-button>
                    </div>
                    <div class="from_container" style="padding: 0;">
                        <el-table :data="merchantsByConditions" border style="width: 100%" max-height="550"
                            :row-class-name="tableRowClassName" @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" align="center" />
                            <el-table-column label="店铺名" prop="merchantName" align="center" />
                            <el-table-column label="地区" prop="county" align="center" width="200" />
                            <el-table-column label="操作" prop="assessmentType" width="100" align="center">
                                <template #default="scope">
                                    <div class="table-btn">
                                        <el-button class="table-btn-delete" link
                                            @click="merchantsDelete(scope.row)">删除</el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="merchants_right">
                    <div class="store_content_header">
                        <div class="title_line"></div>
                        <div class="title">路线规划</div>
                    </div>
                    <div class="map_img">
                        <div :id="'container' + monthlyId" style="height: 100%;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-footer">
            <el-button class="upload_submit" @click="submitForm">确 定</el-button>
            <el-button class="upload_cancel" @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue'
import cache from '@/plugins/cache'
import { deptTreeSelect } from "@/api/system/user"
import { saveSelectedStaffAndMerchants, listMerchantsByMonthlyId } from "@/api/quarterlyassessments/quarterlyassessments"
import { selectMerchants } from "@/api/merchant/info"
import { ElLoading, ElMessage } from 'element-plus'
import mapStart from '@/assets/images/map/start.png'
import mapMid from '@/assets/images/map/mid.png'
import mapEnd from '@/assets/images/map/end.png'

const { proxy } = getCurrentInstance()
const { merchant_jygm, merchant_cxdj, merchant_zdlx } = proxy.useDict("merchant_jygm", "merchant_cxdj", "merchant_zdlx")
const data = cache.session.getJSON('configData')
const monthlyId = data.id;
const name = data.name;
const quarterId = data.quarterId;
const inspectedHouseholds = data.inspectedHouseholds || 0;
const jsonData = ref(data.jsonData);
const queryParams = ref({})
const deptOptions = ref([])
const merchantsByConditions = ref([])
const ids = ref([])
const deptId = ref('')
const deptOptionsList = ref([])
const newMerchantsByConditions = ref([])
const pointList = ref([])

onMounted(() => {
    // 加载已保存的月度考核数据（人员配置和商户数据）
    loadMonthlyAssessmentData()

    // 加载用户列表和部门树
    loadUserLists()
    getDeptTree()
})
const initMap = () => {
    const map = new AMap.Map('container' + monthlyId, {
        resizeEnable: true,
        center: [115.042297, 35.759642] // 初始中心点坐标
    });
    map.on('complete', async function () {
        if (!merchantsByConditions.value.length) return
        let lineList = []
        let list = []
        if (jsonData.value) {
            lineList = JSON.parse(jsonData.value);
            list = merchantsByConditions.value
        } else {
            const options = JSON.parse(JSON.stringify(merchantsByConditions.value))
            options.unshift({ longitudeAfterOffset: '115.042297', latitudeAfterOffset: '35.759642' })
            const arr = splitPointsIntoSegments(options)
            const arr2 = []
            for (let i = 0; i < arr.length; i++) {
                const path = await getDrivingPath(arr[i])
                arr2.push(path)
            }
            lineList = arr2.flat();
            list = arr.flat();
        }
        usePolyline(map, lineList, list)
    });
}

function splitPointsIntoSegments(points, segmentSize = 15) {
    if (!Array.isArray(points) || points.length === 0) {
        return [];
    }
    const segments = [];
    let startIndex = 0;
    while (startIndex < points.length) {
        let endIndex = startIndex + segmentSize;
        if (endIndex > points.length) {
            endIndex = points.length;
        }
        const segment = points.slice(startIndex, endIndex);
        segments.push(segment);
        startIndex = endIndex - 1;
        if (startIndex >= points.length - 1) {
            break;
        }
    }
    return segments;
}

const usePolyline = (map, path, list) => {
    const arrowSymbol = {
        path: 'M 0,-2 L 4,0 L 0,2 Z',  // SVG路径定义箭头形状
        fillColor: 'white',            // 填充色
        fillOpacity: 1,                // 不透明度
        scale: 1.5,                    // 缩放比例
        strokeColor: 'white',          // 描边色
        strokeWeight: 1                // 描边宽度
    };
    const polyline = new AMap.Polyline({
        path,              // 设置路径点
        strokeColor: "#0091ff", // 自定义颜色
        strokeWeight: 6,        // 宽度
        strokeOpacity: 1,     // 透明度
        strokeStyle: "solid",   // 实线
        lineJoin: "round",      // 折线拐点圆角
        icons: [{
            icon: arrowSymbol,
            offset: '10%',    // 起始位置
            repeat: '50px'    // 每50像素重复一个箭头
        }],
        dirColor: "white",       // 方向箭头颜色
        showDir: true            // 显示方向箭头
    });
    const len = list.length - 1;
    const startIcon = new AMap.Icon({
        size: new AMap.Size(25, 30),
        image: mapStart,
        imageSize: new AMap.Size(25, 30),
    });
    const startMarker = new AMap.Marker({
        position: new AMap.LngLat(115.042297, 35.759642),
        icon: startIcon,
        offset: new AMap.Pixel(-13, -30)
    });
    const endIcon = new AMap.Icon({
        size: new AMap.Size(25, 30),
        image: mapEnd,
        imageSize: new AMap.Size(25, 30),
    });
    const endMarker = new AMap.Marker({
        position: new AMap.LngLat(list[len].longitudeAfterOffset, list[len].latitudeAfterOffset),
        icon: endIcon,
        offset: new AMap.Pixel(-13, -30)
    });
    const markers = [];
    const marIcon = new AMap.Icon({
        size: new AMap.Size(25, 30),
        image: mapMid,
        imageSize: new AMap.Size(25, 30),
    });
    for (let i = 0; i < len; i++) {
        markers.push({ position: [list[i].longitudeAfterOffset, list[i].latitudeAfterOffset] })
    }
    markers.forEach(function (marker) {
        new AMap.Marker({
            map: map,
            icon: marIcon,
            position: [marker.position[0], marker.position[1]],
            offset: new AMap.Pixel(-13, -30)
        });
    });
    map.add([startMarker, endMarker]);
    pointList.value = path
    polyline.setMap(map);
    map.setFitView([polyline]);
}

const getDrivingPath = (list) => {
    return new Promise((resolve, reject) => {
        const driving = new AMap.Driving({
            policy: AMap.DrivingPolicy.LEAST_TIME, // 策略设为最少时间
            showTraffic: false, // 关闭交通路况显示
        });
        const start = [list[0].longitudeAfterOffset, list[0].latitudeAfterOffset]; // 起点坐标
        const waypoints = [];// 途经点坐标数组
        const len = list.length - 1;
        for (let i = 0; i < len; i++) {
            waypoints.push([list[i].longitudeAfterOffset, list[i].latitudeAfterOffset])
        }
        const end = [list[len].longitudeAfterOffset, list[len].latitudeAfterOffset]; // 终点坐标
        driving.search(start, end, {
            waypoints: waypoints, // 设置途经点
            policy: AMap.DrivingPolicy.LEAST_TIME // 设置策略为最少时间
        }, function (status, result) {
            if (status === 'complete') {
                const route = result.routes[0];
                let path = [];
                route.steps.forEach(step => {
                    path = path.concat(step.path);
                });
                resolve(path)
            } else {
                console.log('获取驾车数据失败：' + result)
            }
        });
    })
}
/** 加载已保存的月度考核数据 */
async function loadMonthlyAssessmentData() {
    try {
        // 获取已配置的商户列表
        const merchantResponse = await listMerchantsByMonthlyId(monthlyId)
        if (merchantResponse.code === 200 && merchantResponse.data) {
            // 转换商户数据格式以适配现有的表格显示
            const merchants = merchantResponse.data.map(item => ({
                id: item.merchantInfo.id,
                merchantName: item.merchantInfo.merchantName,
                county: item.merchantInfo.county,
                countyId: item.merchantInfo.countyId,
                chengxindengji: item.merchantInfo.chengxindengji,
                zhongduancengji: item.merchantInfo.zhongduancengji,
                jingyingguimo: item.merchantInfo.jingyingguimo,
                longitudeAfterOffset: item.merchantInfo.longitudeAfterOffset,
                latitudeAfterOffset: item.merchantInfo.latitudeAfterOffset
                // 其他商户信息字段...
            }))
            merchantsByConditions.value = merchants
            newMerchantsByConditions.value = JSON.parse(JSON.stringify(merchants))
            initMap()
        }
    } catch (error) {
        console.error('加载月度考核数据失败:', error)
        // 不显示错误提示，因为可能是首次创建，没有保存的数据
    }
}
/** 查询地区下拉树结构 */
const getDeptTree = () => {
    deptTreeSelect().then(response => {
        deptOptions.value = filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
        let list = []
        response.data.forEach(item => {
            if (item.children && item.children.length > 0) {
                item.children.forEach(child => {
                    list.push({
                        value: child.id,
                        label: child.label
                    })
                })
            }
        })
        deptOptionsList.value = list
    })
}
/** 过滤禁用的地区 */
const filterDisabledDept = (deptList) => {
    return deptList.filter(dept => {
        if (dept.disabled) {
            return false
        }
        if (dept.children && dept.children.length) {
            dept.children = filterDisabledDept(dept.children)
        }
        return true
    })
}
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}
const chooseCounty = (value) => {
    if (value) {
        merchantsByConditions.value = newMerchantsByConditions.value.filter(el => el.countyId === value)
    } else merchantsByConditions.value = newMerchantsByConditions.value
}
const handleQuery = () => {
    const loading = ElLoading.service({
        lock: true,
        text: '正在抽取中，请稍后...',
        background: 'rgba(0, 0, 0, 0.7)',
    })
    if (!queryParams.value.num) delete queryParams.value.num
    selectMerchants(queryParams.value).then(res => {
        if (res.code === 200) {
            merchantsByConditions.value = res.data.merchants || []
            newMerchantsByConditions.value = JSON.parse(JSON.stringify(merchantsByConditions.value))
            jsonData.value = '';
            initMap()
            loading.close()
        }
    })
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item)
}
const deleteAll = () => {
    if (ids.value.length > 1) {
        proxy.$modal.confirm('是否确认删除？').then(() => {
            for (let i = 0; i < ids.value.length; i++) {
                const index = merchantsByConditions.value.findIndex(el => el.id === ids.value[i].id)
                merchantsByConditions.value.splice(index, 1)
            }
            newMerchantsByConditions.value = JSON.parse(JSON.stringify(merchantsByConditions.value))
            proxy.$modal.msgSuccess("删除成功")
        }).catch(() => { })
    } else merchantsDelete(ids.value[0])
}
const merchantsDelete = (row) => {
    proxy.$modal.confirm('是否确认删除"' + row.merchantName + '"的商户？').then(() => {
        const index = merchantsByConditions.value.findIndex(el => el.id === row.id)
        merchantsByConditions.value.splice(index, 1)
        newMerchantsByConditions.value = JSON.parse(JSON.stringify(merchantsByConditions.value))
        proxy.$modal.msgSuccess("删除成功")
    }).catch(() => { })
}
const submitForm = () => {
    if (inspectedHouseholds > 0 && merchantsByConditions.value.length < inspectedHouseholds) {
        return ElMessage({
            message: `考核商户不得少于${inspectedHouseholds}条!`,
            type: 'warning',
        })
    }
    const merchantData = {
        monthlyAssessmentId: parseInt(monthlyId),
        selectedMerchants: merchantsByConditions.value,
        selectionType: "0"  // 使用字典值：0-考核商户，1-自主添加商户，2-自主添加无证户
    }

    const submitData = {
        merchantData
    }
    if (pointList.value.length) submitData.jsonData = JSON.stringify(pointList.value)
    saveSelectedStaffAndMerchants(submitData).then(response => {
        if (response.code === 200) {
            proxy.$modal.msgSuccess("保存成功")
            cancel()
        } else {
            proxy.$modal.msgError(response.msg || "保存失败")
        }
    }).catch(error => {
        console.error('保存失败:', error)
        proxy.$modal.msgError("保存失败，请重试")
    })
}

const cancel = () => {
    const params = {
        path: "/assessment/quarterlyassessments/childs/configuration",
        query: {
            id: quarterId
        }
    }
    proxy.$tab.closeOpenPage(params)
}
</script>

<style lang="scss" scoped>
.app-container {
    background-color: #f4f8ff;
    color: #333333;
}

.store_header {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;

    .store_header_title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
    }
}

.store_content {
    box-sizing: border-box;
    padding: 20px 30px;
    background-color: #fff;
    border-radius: 5px;
    margin-top: 10px;

    .store_content_header {
        display: flex;
        align-items: center;

        .title_line {
            width: 3px;
            height: 19px;
            background: #21A042;
            border-radius: 2px;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-left: 10px;
        }
    }

    .store_content_form {
        margin-top: 25px;
    }
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 30px;
    background-color: #fff;
    margin-top: 10px;

    .upload_submit,
    .upload_cancel {
        box-sizing: border-box;
        width: 140px;
        height: 45px;
        font-size: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
    }

    .upload_submit {
        color: #fff;
        background-color: #21A042;
    }

    .upload_cancel {
        border: 1px solid #21A042;
        color: #21A042;
    }
}

.from_container {
    min-height: 120px;
}

.from_header {
    padding-left: 0;
}

.btn-search {
    width: 126px;
}

.merchants {
    display: flex;
    border-top: 1px solid #f5f5f5;

    .merchants_left {
        width: 55%;
        padding: 30px;
        border-right: 1px solid #f5f5f5;

        .merchants_left_header {
            display: flex;
            align-items: center;
            justify-content: space-between;

            :deep(.el-select) {
                width: 220px;

                .el-select__wrapper {
                    box-shadow: 0 0 0 0 !important;
                    background-color: #f7f8fa;

                    .el-icon {
                        color: #00b578;
                    }
                }
            }

            .header__left {
                display: flex;
                align-items: center;
            }
        }
    }

    .merchants_right {
        padding: 30px;

        .map_img {
            width: 686px;
            height: 554px;
            margin-top: 20px;
        }
    }
}
</style>