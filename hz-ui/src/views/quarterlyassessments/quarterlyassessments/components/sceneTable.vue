<template>
    <div class="main">
        <el-table :data="monthlyAssessmentsList" border style="width: 100%" max-height="300"
            :row-class-name="tableRowClassName">
            <el-table-column label="名称" prop="name" width="150" align="center" />
            <el-table-column label="考核类型" prop="assessmentType" width="100" align="center">
                <template #default="scope">
                    <el-tag type="primary" size="small" v-if="scope.row.assessmentType !== null">{{ scope.row.assessmentType }}</el-tag>
                    <div v-else>-</div>
                </template>
            </el-table-column>
            <el-table-column label="考核县区" align="center" width="120">
                <template #default="scope">
                    城区分公司
                </template>
            </el-table-column>
            <el-table-column label="检查户数" prop="inspectedHouseholds" align="center" />
            <el-table-column label="组长" align="center">
                <template #default="scope">
                    {{ scope.row.staffMap ? scope.row.staffMap['组长'] : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="专卖人员" align="center">
                <template #default="scope">
                    {{ scope.row.staffMap ? scope.row.staffMap['专卖人员'] : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="营销人员" align="center">
                <template #default="scope">
                    {{ scope.row.staffMap ? scope.row.staffMap['营销人员'] : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="监督人员" align="center">
                <template #default="scope">
                    {{ scope.row.staffMap ? scope.row.staffMap['监督人员'] : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="statusText" align="center" />
            <el-table-column label="创建人" prop="createBy" align="center" />
            <el-table-column label="创建时间" prop="createTime" align="center" width="180" />
            <el-table-column label="操作" prop="assessmentType" width="400" align="center">
                <template #default="scope">
                    <div class="table-btn">
                        <el-button v-if="scope.row.status == 1" class="table-btn-edit" link
                            @click="handleUpdate(scope.row, 'up')">修改</el-button>
                        <div v-if="scope.row.status == 1" class="btn-line"></div>
                        <el-button v-if="scope.row.status == 1" class="table-btn-edit" link
                            @click="handleUpdate(scope.row, 'release')">发布</el-button>
                        <div v-if="scope.row.status == 1" class="btn-line"></div>
                        <!-- <el-button class="table-btn-edit" link
                            @click="handleUpdate(scope.row, 'county')">县域成绩</el-button>
                        <div class="btn-line"></div> -->
                        <el-button class="table-btn-edit" link
                            @click="handleUpdate(scope.row, 'merchant')">商户成绩</el-button>
                        <div class="btn-line" v-if="scope.row.status == 2"></div>
                        <el-button v-if="scope.row.status == 2" class="table-btn-delete" link
                            @click="handleUpdate(scope.row, 'unRelease')">撤销发布</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup>
const props = defineProps({
    monthlyAssessmentsList: {
        type: Array,
        default: []
    }
})
const emits = defineEmits(['handleUpdate'])
// table样式
const tableRowClassName = ({ row, rowIndex }) => {
    if ((rowIndex + 1) % 2 === 0) {
        return 'table-row'
    }
    return ''
}

const handleUpdate = (row, type) => {
    emits('handleUpdate', row, type)
}
</script>