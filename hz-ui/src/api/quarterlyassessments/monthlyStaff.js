import request from '@/utils/request'

// 查询月度考核人员列表
export function listMonthlyStaff(query) {
  return request({
    url: '/quarterlyassessments/staff/list',
    method: 'get',
    params: query
  })
}

// 获取可用用户列表
export function getUserList() {
  return request({
    url: '/quarterlyassessments/staff/users',
    method: 'get'
  })
}

// 获取角色列表
export function getRoleList() {
  return request({
    url: '/quarterlyassessments/staff/roles',
    method: 'get'
  })
}

// 随机抽取组长
export function selectRandomLeaders(data) {
  return request({
    url: '/quarterlyassessments/staff/selectLeaders',
    method: 'post',
    data: data
  })
}

// 随机抽取监督人员
export function selectRandomSupervisors(data) {
  return request({
    url: '/quarterlyassessments/staff/selectSupervisors',
    method: 'post',
    data: data
  })
}

// 一键抽取人员（组长+监督人员）
export function selectAllStaff(data) {
  return request({
    url: '/quarterlyassessments/staff/selectAllStaff',
    method: 'post',
    data: data
  })
}

// 保存抽取的人员
export function saveSelectedStaff(data) {
  return request({
    url: '/quarterlyassessments/staff/saveSelectedStaff',
    method: 'post',
    data: data
  })
}

// 更新月度考核人员
export function updateMonthlyStaff(data) {
  return request({
    url: '/quarterlyassessments/staff/monthly',
    method: 'put',
    data: data
  })
} 