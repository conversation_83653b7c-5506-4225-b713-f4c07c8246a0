<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hz.merchant.mapper.MerchantInfoMapper">
    
    <resultMap type="MerchantInfo" id="MerchantInfoResult">
        <result property="id"    column="id"    />
        <result property="provinces"    column="provinces"    />
        <result property="licence"    column="licence"    />
        <result property="merchantCode"    column="merchant_code"    />
        <result property="merchantName"    column="merchant_name"    />
        <result property="legalName"    column="legal_name"    />
        <result property="merchantStatue"    column="merchant_statue"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="county"    column="county"    />
        <result property="countyId"    column="county_id"    />
        <result property="shichangbu"    column="shichangbu"    />
        <result property="yingxiaoxian"    column="yingxiaoxian"    />
        <result property="businessAddress"    column="business_address"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="marketType"    column="market_type"    />
        <result property="marketTypeSegment"    column="market_type_segment"    />
        <result property="yetai"    column="yetai"    />
        <result property="jingyingguimo"    column="jingyingguimo"    />
        <result property="shangquan"    column="shangquan"    />
        <result property="dinghuozhouqileixing"    column="dinghuozhouqileixing"    />
        <result property="dinghuori"    column="dinghuori"    />
        <result property="dinghuofangshi"    column="dinghuofangshi"    />
        <result property="jiesuanfangshi"    column="jiesuanfangshi"    />
        <result property="wangshangjiesuan"    column="wangshangjiesuan"    />
        <result property="chengxindengji"    column="chengxindengji"    />
        <result property="dangweibianma"    column="dangweibianma"    />
        <result property="dangwei"    column="dangwei"    />
        <result property="ruwangriqi"    column="ruwangriqi"    />
        <result property="zhongduancengji"    column="zhongduancengji"    />
        <result property="zhongduanleibie"    column="zhongduanleibie"    />
        <result property="zhongduanleibiexifen"    column="zhongduanleibiexifen"    />
        <result property="xuejiadangwei"    column="xuejiadangwei"    />
        <result property="xuejiayanzhongduanleixing"    column="xuejiayanzhongduanleixing"    />
        <result property="creatId"    column="creat_id"    />
        <result property="creatBy"    column="creat_by"    />
        <result property="photo"    column="photo"    />
        <result property="creatTime"    column="creat_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="originalLongitude"    column="original_longitude"    />
        <result property="originalLatitude"    column="original_latitude"    />
        <result property="longitudeAfterOffset"    column="longitude_after_offset"    />
        <result property="latitudeAfterOffset"    column="latitude_after_offset"    />
        <result property="address"    column="address"    />
        <result property="lastAssessedDate"    column="last_assessed_date"    />
    </resultMap>

    <sql id="selectMerchantInfoVo">
        select mi.id, mi.provinces, mi.licence, mi.merchant_name, mi.legal_name, mi.merchant_statue, mi.phone_number, mi.county, mi.county_id, mi.shichangbu, mi.yingxiaoxian, mi.business_address, mi.business_scope, mi.market_type, mi.market_type_segment, mi.yetai, mi.jingyingguimo, mi.shangquan, mi.dinghuozhouqileixing, mi.dinghuori, mi.dinghuofangshi, mi.jiesuanfangshi, mi.wangshangjiesuan, mi.chengxindengji, mi.dangweibianma, mi.dangwei, mi.ruwangriqi, mi.zhongduancengji, mi.zhongduanleibie, mi.zhongduanleibiexifen, mi.xuejiadangwei, mi.xuejiayanzhongduanleixing, mi.creat_id, mi.creat_by, mi.photo, mi.creat_time, mi.update_id, mi.update_by, mi.update_time, mi.remark, mi.last_assessed_date, mi.original_longitude, mi.original_latitude, mi.longitude_after_offset, mi.latitude_after_offset, mi.address
        from merchant_info mi
    </sql>

    <select id="selectMerchantInfoList" parameterType="MerchantInfo" resultMap="MerchantInfoResult">
        select mi.id, mi.provinces, mi.licence, mi.merchant_name, mi.legal_name, mi.merchant_statue, mi.phone_number,
        mi.county, mi.county_id, mi.shichangbu, mi.yingxiaoxian, mi.business_address, mi.business_scope,
        mi.market_type, mi.market_type_segment, mi.yetai, mi.jingyingguimo, mi.shangquan, mi.dinghuozhouqileixing,
        mi.dinghuori, mi.dinghuofangshi, mi.jiesuanfangshi, mi.wangshangjiesuan, mi.chengxindengji, mi.dangweibianma,
        mi.dangwei, mi.ruwangriqi, mi.zhongduancengji, mi.zhongduanleibie, mi.zhongduanleibiexifen, mi.xuejiadangwei,
        mi.xuejiayanzhongduanleixing, mi.creat_id, mi.creat_by, mi.photo, mi.creat_time, mi.update_id, mi.update_by, mi.update_time,
        mi.remark, mi.last_assessed_date, mi.original_longitude, mi.original_latitude, mi.longitude_after_offset, mi.latitude_after_offset, mi.address from merchant_info mi
        <!-- left join sys_dept d on mi.county_id = d.dept_id -->
        <where>
            <if test="provinces != null  and provinces != ''"> and mi.provinces = #{provinces}</if>
            <if test="licence != null "> and mi.licence = #{licence}</if>
            <if test="merchantName != null  and merchantName != ''"> and mi.merchant_name like concat('%', #{merchantName}, '%')</if>
            <if test="legalName != null  and legalName != ''"> and mi.legal_name like concat('%', #{legalName}, '%')</if>
            <if test="merchantStatue != null  and merchantStatue != ''"> and mi.merchant_statue = #{merchantStatue}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and mi.phone_number = #{phoneNumber}</if>
            <if test="county != null  and county != ''"> and mi.county = #{county}</if>
            <if test="countyId != null "> and mi.county_id = #{countyId}</if>
            <if test="shichangbu != null  and shichangbu != ''"> and mi.shichangbu = #{shichangbu}</if>
            <if test="yingxiaoxian != null  and yingxiaoxian != ''"> and mi.yingxiaoxian = #{yingxiaoxian}</if>
            <if test="businessAddress != null  and businessAddress != ''"> and mi.business_address = #{businessAddress}</if>
            <if test="businessScope != null  and businessScope != ''"> and mi.business_scope = #{businessScope}</if>
            <if test="marketType != null  and marketType != ''"> and mi.market_type = #{marketType}</if>
            <if test="marketTypeSegment != null  and marketTypeSegment != ''"> and mi.market_type_segment = #{marketTypeSegment}</if>
            <if test="yetai != null  and yetai != ''"> and mi.yetai = #{yetai}</if>
            <if test="jingyingguimo != null  and jingyingguimo != ''"> and mi.jingyingguimo = #{jingyingguimo}</if>
            <if test="shangquan != null  and shangquan != ''"> and mi.shangquan = #{shangquan}</if>
            <if test="dinghuozhouqileixing != null  and dinghuozhouqileixing != ''"> and mi.dinghuozhouqileixing = #{dinghuozhouqileixing}</if>
            <if test="dinghuori != null  and dinghuori != ''"> and mi.dinghuori = #{dinghuori}</if>
            <if test="dinghuofangshi != null  and dinghuofangshi != ''"> and mi.dinghuofangshi = #{dinghuofangshi}</if>
            <if test="jiesuanfangshi != null  and jiesuanfangshi != ''"> and mi.jiesuanfangshi = #{jiesuanfangshi}</if>
            <if test="wangshangjiesuan != null  and wangshangjiesuan != ''"> and mi.wangshangjiesuan = #{wangshangjiesuan}</if>
            <if test="chengxindengji != null  and chengxindengji != ''"> and mi.chengxindengji = #{chengxindengji}</if>
            <if test="dangweibianma != null "> and mi.dangweibianma = #{dangweibianma}</if>
            <if test="dangwei != null  and dangwei != ''"> and mi.dangwei = #{dangwei}</if>
            <if test="ruwangriqi != null "> and mi.ruwangriqi = #{ruwangriqi}</if>
            <if test="zhongduancengji != null  and zhongduancengji != ''"> and mi.zhongduancengji = #{zhongduancengji}</if>
            <if test="zhongduanleibie != null  and zhongduanleibie != ''"> and mi.zhongduanleibie = #{zhongduanleibie}</if>
            <if test="zhongduanleibiexifen != null  and zhongduanleibiexifen != ''"> and mi.zhongduanleibiexifen = #{zhongduanleibiexifen}</if>
            <if test="xuejiadangwei != null  and xuejiadangwei != ''"> and mi.xuejiadangwei = #{xuejiadangwei}</if>
            <if test="xuejiayanzhongduanleixing != null  and xuejiayanzhongduanleixing != ''"> and mi.xuejiayanzhongduanleixing = #{xuejiayanzhongduanleixing}</if>
            <if test="creatId != null "> and mi.creat_id = #{creatId}</if>
            <if test="creatBy != null  and creatBy != ''"> and mi.creat_by = #{creatBy}</if>
            <if test="photo != null  and photo != ''"> and mi.photo = #{photo}</if>
            <if test="creatTime != null "> and mi.creat_time = #{creatTime}</if>
            <if test="updateId != null "> and mi.update_id = #{updateId}</if>
            <if test="updateBy != null  and updateBy != ''"> and mi.update_by = #{updateBy}</if>
            <if test="updateTime != null "> and mi.update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''"> and mi.remark = #{remark}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectMerchantInfoById" parameterType="Long" resultMap="MerchantInfoResult">
        <include refid="selectMerchantInfoVo"/>
        where mi.id = #{id}
    </select>

    <select id="selectMerchantInfoByLicence" parameterType="String" resultMap="MerchantInfoResult">
        <include refid="selectMerchantInfoVo"/>
        where mi.licence = #{licence} limit 1
    </select>

    <select id="selectMerchantInfoByIds" resultMap="MerchantInfoResult">
        <include refid="selectMerchantInfoVo"/>
        where mi.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertMerchantInfo" parameterType="MerchantInfo" useGeneratedKeys="true" keyProperty="id">
        insert into merchant_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="provinces != null">provinces,</if>
            <if test="licence != null">licence,</if>
            <if test="merchantName != null and merchantName != ''">merchant_name,</if>
            <if test="legalName != null">legal_name,</if>
            <if test="merchantStatue != null">merchant_statue,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="county != null">county,</if>
            <if test="countyId != null">county_id,</if>
            <if test="shichangbu != null">shichangbu,</if>
            <if test="yingxiaoxian != null">yingxiaoxian,</if>
            <if test="businessAddress != null">business_address,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="marketType != null">market_type,</if>
            <if test="marketTypeSegment != null">market_type_segment,</if>
            <if test="yetai != null">yetai,</if>
            <if test="jingyingguimo != null">jingyingguimo,</if>
            <if test="shangquan != null">shangquan,</if>
            <if test="dinghuozhouqileixing != null">dinghuozhouqileixing,</if>
            <if test="dinghuori != null">dinghuori,</if>
            <if test="dinghuofangshi != null">dinghuofangshi,</if>
            <if test="jiesuanfangshi != null">jiesuanfangshi,</if>
            <if test="wangshangjiesuan != null">wangshangjiesuan,</if>
            <if test="chengxindengji != null">chengxindengji,</if>
            <if test="dangweibianma != null">dangweibianma,</if>
            <if test="dangwei != null">dangwei,</if>
            <if test="ruwangriqi != null">ruwangriqi,</if>
            <if test="zhongduancengji != null">zhongduancengji,</if>
            <if test="zhongduanleibie != null">zhongduanleibie,</if>
            <if test="zhongduanleibiexifen != null">zhongduanleibiexifen,</if>
            <if test="xuejiadangwei != null">xuejiadangwei,</if>
            <if test="xuejiayanzhongduanleixing != null">xuejiayanzhongduanleixing,</if>
            <if test="creatId != null">creat_id,</if>
            <if test="creatBy != null">creat_by,</if>
            <if test="photo != null">photo,</if>
            <if test="creatTime != null">creat_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="provinces != null">#{provinces},</if>
            <if test="licence != null">#{licence},</if>
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="legalName != null">#{legalName},</if>
            <if test="merchantStatue != null">#{merchantStatue},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="county != null">#{county},</if>
            <if test="countyId != null">#{countyId},</if>
            <if test="shichangbu != null">#{shichangbu},</if>
            <if test="yingxiaoxian != null">#{yingxiaoxian},</if>
            <if test="businessAddress != null">#{businessAddress},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="marketType != null">#{marketType},</if>
            <if test="marketTypeSegment != null">#{marketTypeSegment},</if>
            <if test="yetai != null">#{yetai},</if>
            <if test="jingyingguimo != null">#{jingyingguimo},</if>
            <if test="shangquan != null">#{shangquan},</if>
            <if test="dinghuozhouqileixing != null">#{dinghuozhouqileixing},</if>
            <if test="dinghuori != null">#{dinghuori},</if>
            <if test="dinghuofangshi != null">#{dinghuofangshi},</if>
            <if test="jiesuanfangshi != null">#{jiesuanfangshi},</if>
            <if test="wangshangjiesuan != null">#{wangshangjiesuan},</if>
            <if test="chengxindengji != null">#{chengxindengji},</if>
            <if test="dangweibianma != null">#{dangweibianma},</if>
            <if test="dangwei != null">#{dangwei},</if>
            <if test="ruwangriqi != null">#{ruwangriqi},</if>
            <if test="zhongduancengji != null">#{zhongduancengji},</if>
            <if test="zhongduanleibie != null">#{zhongduanleibie},</if>
            <if test="zhongduanleibiexifen != null">#{zhongduanleibiexifen},</if>
            <if test="xuejiadangwei != null">#{xuejiadangwei},</if>
            <if test="xuejiayanzhongduanleixing != null">#{xuejiayanzhongduanleixing},</if>
            <if test="creatId != null">#{creatId},</if>
            <if test="creatBy != null">#{creatBy},</if>
            <if test="photo != null">#{photo},</if>
            <if test="creatTime != null">#{creatTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMerchantInfo" parameterType="MerchantInfo">
        update merchant_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="provinces != null">provinces = #{provinces},</if>
            <if test="merchantName != null and merchantName != ''">merchant_name = #{merchantName},</if>
            <if test="legalName != null">legal_name = #{legalName},</if>
            <if test="merchantStatue != null">merchant_statue = #{merchantStatue},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="county != null">county = #{county},</if>
            <if test="countyId != null">county_id = #{countyId},</if>
            <if test="shichangbu != null">shichangbu = #{shichangbu},</if>
            <if test="yingxiaoxian != null">yingxiaoxian = #{yingxiaoxian},</if>
            <if test="businessAddress != null">business_address = #{businessAddress},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="marketType != null">market_type = #{marketType},</if>
            <if test="marketTypeSegment != null">market_type_segment = #{marketTypeSegment},</if>
            <if test="yetai != null">yetai = #{yetai},</if>
            <if test="jingyingguimo != null">jingyingguimo = #{jingyingguimo},</if>
            <if test="shangquan != null">shangquan = #{shangquan},</if>
            <if test="dinghuozhouqileixing != null">dinghuozhouqileixing = #{dinghuozhouqileixing},</if>
            <if test="dinghuori != null">dinghuori = #{dinghuori},</if>
            <if test="dinghuofangshi != null">dinghuofangshi = #{dinghuofangshi},</if>
            <if test="jiesuanfangshi != null">jiesuanfangshi = #{jiesuanfangshi},</if>
            <if test="wangshangjiesuan != null">wangshangjiesuan = #{wangshangjiesuan},</if>
            <if test="chengxindengji != null">chengxindengji = #{chengxindengji},</if>
            <if test="dangweibianma != null">dangweibianma = #{dangweibianma},</if>
            <if test="dangwei != null">dangwei = #{dangwei},</if>
            <if test="ruwangriqi != null">ruwangriqi = #{ruwangriqi},</if>
            <if test="zhongduancengji != null">zhongduancengji = #{zhongduancengji},</if>
            <if test="zhongduanleibie != null">zhongduanleibie = #{zhongduanleibie},</if>
            <if test="zhongduanleibiexifen != null">zhongduanleibiexifen = #{zhongduanleibiexifen},</if>
            <if test="xuejiadangwei != null">xuejiadangwei = #{xuejiadangwei},</if>
            <if test="xuejiayanzhongduanleixing != null">xuejiayanzhongduanleixing = #{xuejiayanzhongduanleixing},</if>
            <if test="creatId != null">creat_id = #{creatId},</if>
            <if test="creatBy != null">creat_by = #{creatBy},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="creatTime != null">creat_time = #{creatTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMerchantInfoById" parameterType="Long">
        delete from merchant_info where id = #{id}
    </delete>

    <delete id="deleteMerchantInfoByIds" parameterType="Long">
        delete from merchant_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateMerchantStatus" parameterType="MerchantInfo">
        update merchant_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantStatue != null">merchant_statue = #{merchantStatue},</if>
        </trim>
        where id = #{id}
    </update>


    <!-- 数据采集：商户经纬度 -->
    <select id="selectMerchantLocationListBySjcj" resultMap="MerchantInfoResult">
        select mi.merchant_code, mi.longitude_after_offset, mi.latitude_after_offset from merchant_info mi
        <if test="classify != null and classify == 'price'">
            right join (select sh_xkz from gather_st_price where st_id = #{stId} GROUP BY sh_xkz) temp on temp.sh_xkz = mi.merchant_code
        </if>
        <if test="classify != null and classify == 'inventory'">
            right join (select sh_xkz from gather_st_inventory where st_id = #{stId} GROUP BY sh_xkz) temp on temp.sh_xkz = mi.merchant_code
        </if>
    </select>

    <!-- 查询具有有效坐标的商户信息列表 -->
    <select id="selectMerchantInfoListWithValidCoordinates" parameterType="MerchantInfo" resultMap="MerchantInfoResult">
        <include refid="selectMerchantInfoVo"/>
        <where>
            <!-- 过滤有效坐标：经纬度不为空且不为0 -->
            and mi.longitude_after_offset is not null
            and mi.latitude_after_offset is not null
            and mi.longitude_after_offset != 0
            and mi.latitude_after_offset != 0

            <!-- 其他查询条件 -->
            <if test="provinces != null  and provinces != ''"> and mi.provinces = #{provinces}</if>
            <if test="licence != null "> and mi.licence = #{licence}</if>
            <if test="merchantName != null  and merchantName != ''"> and mi.merchant_name like concat('%', #{merchantName}, '%')</if>
            <if test="legalName != null  and legalName != ''"> and mi.legal_name like concat('%', #{legalName}, '%')</if>
            <if test="merchantStatue != null  and merchantStatue != ''"> and mi.merchant_statue = #{merchantStatue}</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and mi.phone_number = #{phoneNumber}</if>
            <if test="county != null  and county != ''"> and mi.county = #{county}</if>
            <if test="countyId != null "> and mi.county_id = #{countyId}</if>
            <if test="shichangbu != null  and shichangbu != ''"> and mi.shichangbu = #{shichangbu}</if>
            <if test="yingxiaoxian != null  and yingxiaoxian != ''"> and mi.yingxiaoxian = #{yingxiaoxian}</if>
            <if test="businessAddress != null  and businessAddress != ''"> and mi.business_address = #{businessAddress}</if>
            <if test="businessScope != null  and businessScope != ''"> and mi.business_scope = #{businessScope}</if>
            <if test="marketType != null  and marketType != ''"> and mi.market_type = #{marketType}</if>
            <if test="marketTypeSegment != null  and marketTypeSegment != ''"> and mi.market_type_segment = #{marketTypeSegment}</if>
            <if test="yetai != null  and yetai != ''"> and mi.yetai = #{yetai}</if>
            <if test="jingyingguimo != null  and jingyingguimo != ''"> and mi.jingyingguimo = #{jingyingguimo}</if>
            <if test="shangquan != null  and shangquan != ''"> and mi.shangquan = #{shangquan}</if>
            <if test="dinghuozhouqileixing != null  and dinghuozhouqileixing != ''"> and mi.dinghuozhouqileixing = #{dinghuozhouqileixing}</if>
            <if test="dinghuori != null  and dinghuori != ''"> and mi.dinghuori = #{dinghuori}</if>
            <if test="dinghuofangshi != null  and dinghuofangshi != ''"> and mi.dinghuofangshi = #{dinghuofangshi}</if>
            <if test="jiesuanfangshi != null  and jiesuanfangshi != ''"> and mi.jiesuanfangshi = #{jiesuanfangshi}</if>
            <if test="wangshangjiesuan != null  and wangshangjiesuan != ''"> and mi.wangshangjiesuan = #{wangshangjiesuan}</if>
            <if test="chengxindengji != null  and chengxindengji != ''"> and mi.chengxindengji = #{chengxindengji}</if>
            <if test="dangweibianma != null "> and mi.dangweibianma = #{dangweibianma}</if>
            <if test="dangwei != null  and dangwei != ''"> and mi.dangwei = #{dangwei}</if>
            <if test="ruwangriqi != null "> and mi.ruwangriqi = #{ruwangriqi}</if>
            <if test="zhongduancengji != null  and zhongduancengji != ''"> and mi.zhongduancengji = #{zhongduancengji}</if>
            <if test="zhongduanleibie != null  and zhongduanleibie != ''"> and mi.zhongduanleibie = #{zhongduanleibie}</if>
            <if test="zhongduanleibiexifen != null  and zhongduanleibiexifen != ''"> and mi.zhongduanleibiexifen = #{zhongduanleibiexifen}</if>
            <if test="xuejiadangwei != null  and xuejiadangwei != ''"> and mi.xuejiadangwei = #{xuejiadangwei}</if>
            <if test="xuejiayanzhongduanleixing != null  and xuejiayanzhongduanleixing != ''"> and mi.xuejiayanzhongduanleixing = #{xuejiayanzhongduanleixing}</if>
        </where>
    </select>
</mapper>