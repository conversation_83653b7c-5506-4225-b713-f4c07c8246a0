package com.hz.quarterlyassessments.service;

import java.util.List;
import com.hz.quarterlyassessments.domain.MonthlyAssessmentStaff;
import com.hz.common.core.domain.entity.SysUser;
import com.hz.quarterlyassessments.domain.dto.UpdateMonthlyStaffRequest;

/**
 * 月度考核人员Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-17
 */
public interface IMonthlyAssessmentStaffService 
{
    /**
     * 查询月度考核人员
     * 
     * @param id 月度考核人员主键
     * @return 月度考核人员
     */
    public MonthlyAssessmentStaff selectMonthlyAssessmentStaffById(String id);

    /**
     * 查询月度考核人员列表
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 月度考核人员集合
     */
    public List<MonthlyAssessmentStaff> selectMonthlyAssessmentStaffList(MonthlyAssessmentStaff monthlyAssessmentStaff);

    /**
     * 新增月度考核人员
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 结果
     */
    public int insertMonthlyAssessmentStaff(MonthlyAssessmentStaff monthlyAssessmentStaff);

    /**
     * 修改月度考核人员
     * 
     * @param monthlyAssessmentStaff 月度考核人员
     * @return 结果
     */
    public int updateMonthlyAssessmentStaff(MonthlyAssessmentStaff monthlyAssessmentStaff);

    /**
     * 批量删除月度考核人员
     * 
     * @param ids 需要删除的月度考核人员主键集合
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffByIds(String[] ids);

    /**
     * 删除月度考核人员信息
     * 
     * @param id 月度考核人员主键
     * @return 结果
     */
    public int deleteMonthlyAssessmentStaffById(String id);

    /**
     * 随机抽取组长 - 每年只能被抽中一次
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @param count 抽取数量
     * @return 抽取结果
     */
    public List<SysUser> selectRandomLeaders(Long monthlyAssessmentId, String year, int count);

    /**
     * 随机抽取监督人员 - 每半年只能被抽中一次
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param year 年份
     * @param month 月份
     * @param count 抽取数量
     * @return 抽取结果
     */
    public List<SysUser> selectRandomSupervisors(Long monthlyAssessmentId, String year, int month, int count);

    /**
     * 保存选定的人员到月度考核（包括抽取和手动选择的）
     * 
     * @param monthlyAssessmentId 月度考核ID
     * @param leaders 组长列表
     * @param supervisors 监督人员列表
     * @param monopolyStaffs 专卖人员列表
     * @param marketingStaffs 营销人员列表
     * @param year 年份
     * @param month 月份
     * @return 结果
     */
    public boolean saveSelectedStaff(Long monthlyAssessmentId, List<SysUser> leaders, List<SysUser> supervisors, 
                                   List<SysUser> monopolyStaffs, List<SysUser> marketingStaffs, String year, int month);

    /**
     * 更新月度考核关联的所有人员
     *
     * @param request 更新请求
     * @return 结果
     */
    public boolean updateMonthlyAssessmentStaff(UpdateMonthlyStaffRequest request);
} 