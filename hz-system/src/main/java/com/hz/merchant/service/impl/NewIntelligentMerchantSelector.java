package com.hz.merchant.service.impl;

import com.hz.merchant.config.MerchantSelectionProperties;
import com.hz.merchant.constant.MerchantConstants;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.service.impl.MultiDimensionalQuotaCalculator.MerchantCategory;
import com.hz.merchant.util.GeoUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 新版智能商户选择器
 * 支持12类别配额分配系统
 */
@Service
public class NewIntelligentMerchantSelector {

    private static final Logger logger = LoggerFactory.getLogger(NewIntelligentMerchantSelector.class);

    @Autowired
    private MultiDimensionalQuotaCalculator quotaCalculator;

    @Autowired
    private ThreeDimensionalMerchantClassifier classifier;

    private final Random random = new Random();

    /**
     * 智能选择商户
     * @param allMerchants 所有商户
     * @param properties 配置属性
     * @return 选择结果
     */
    public Solution selectMerchants(List<MerchantInfo> allMerchants, MerchantSelectionProperties properties) {
        logger.info("{}, 总商户数: {}, 目标选择数: {}",
            MerchantConstants.LOG_START_INTELLIGENT_SELECTION,
            allMerchants.size(),
            properties.getTotalCount());

        // 1. 计算12类别配额
        Map<MerchantCategory, Integer> quotas = quotaCalculator.calculateQuotas(properties);

        // 2. 分类所有商户并创建分类缓存
        Map<MerchantCategory, List<MerchantInfo>> classifiedMerchants = classifier.classifyMerchants(allMerchants);

        // 创建分类缓存以避免重复计算
        Map<MerchantInfo, MerchantCategory> classificationCache = new HashMap<>();
        for (Map.Entry<MerchantCategory, List<MerchantInfo>> entry : classifiedMerchants.entrySet()) {
            MerchantCategory category = entry.getKey();
            for (MerchantInfo merchant : entry.getValue()) {
                classificationCache.put(merchant, category);
            }
        }

        // 3. 记录分类统计
        logClassificationStatistics(classifiedMerchants, quotas);

        // 4. 寻找最优解决方案
        Solution bestSolution = findOptimalSolution(classifiedMerchants, quotas, properties);

        if (bestSolution != null) {
            // 设置分类缓存到解决方案中
            bestSolution.setMerchantClassifications(classificationCache);

            logger.info("{}, 选中商户数: {}",
                MerchantConstants.LOG_COMPLETE_INTELLIGENT_SELECTION,
                bestSolution.getMerchants().size());
        }

        return bestSolution;
    }
    
    /**
     * 根据配额选择商户
     */
    private Solution findOptimalSolution(Map<MerchantCategory, List<MerchantInfo>> classifiedMerchants,
                                       Map<MerchantCategory, Integer> quotas,
                                       MerchantSelectionProperties properties) {
        logger.debug("开始商户选择");

        // 直接构建解决方案
        Solution solution = buildSolutionWithQuota(classifiedMerchants, quotas);

        if (solution != null) {
            logger.debug("商户选择完成，选中商户数: {}", solution.getMerchants().size());

            // 计算距离统计信息
            DistanceStatistics distanceStats = calculateDistanceStatistics(solution.getMerchants());
            solution.setTotalDistance(distanceStats.getAverageDistance());
            solution.setDistanceStatistics(distanceStats);
            logger.debug("距离统计 - 平均距离: {:.2f} km, 最大距离: {:.2f} km, 最小距离: {:.2f} km",
                distanceStats.getAverageDistance(),
                distanceStats.getMaxDistance(),
                distanceStats.getMinDistance());
        } else {
            logger.warn("未找到满足配额要求的解决方案");
        }

        return solution;
    }
    
    /**
     * 根据配额构建解决方案
     */
    private Solution buildSolutionWithQuota(Map<MerchantCategory, List<MerchantInfo>> classifiedMerchants, 
                                          Map<MerchantCategory, Integer> quotas) {
        List<MerchantInfo> selectedMerchants = new ArrayList<>();
        Map<MerchantCategory, Integer> actualSelection = new HashMap<>();
        
        for (MerchantCategory category : MerchantCategory.values()) {
            int quota = quotas.get(category);
            if (quota <= 0) continue;
            
            List<MerchantInfo> availableMerchants = classifiedMerchants.get(category);
            if (availableMerchants == null || availableMerchants.isEmpty()) {
                logger.debug("类别 {} 没有可用商户", category.getDescription());
                continue;
            }
            
            // 随机选择商户
            List<MerchantInfo> shuffled = new ArrayList<>(availableMerchants);
            Collections.shuffle(shuffled, random);
            
            int actualCount = Math.min(quota, shuffled.size());
            selectedMerchants.addAll(shuffled.subList(0, actualCount));
            actualSelection.put(category, actualCount);
        }
        
        if (selectedMerchants.isEmpty()) {
            return null;
        }
        
        Solution solution = new Solution();
        solution.setMerchants(selectedMerchants);
        solution.setActualSelection(actualSelection);
        
        return solution;
    }
    
    /**
     * 记录分类统计信息
     */
    private void logClassificationStatistics(Map<MerchantCategory, List<MerchantInfo>> classifiedMerchants,
                                             Map<MerchantCategory, Integer> quotas) {
        logger.info("{}", MerchantConstants.LOG_START_CLASSIFICATION);

        int totalAvailable = 0;
        int totalQuota = 0;

        for (MerchantCategory category : MerchantCategory.values()) {
            List<MerchantInfo> merchants = classifiedMerchants.get(category);
            int available = merchants != null ? merchants.size() : 0;
            int quota = quotas.get(category);

            totalAvailable += available;
            totalQuota += quota;

            logger.debug("类别统计 - {}: 可用={}, 配额={}",
                    category.getDescription(), available, quota);
        }

        logger.info("商户分类统计完成 - 总可用: {}, 总配额: {}", totalAvailable, totalQuota);
    }
    
    /**
     * 计算距离统计信息
     * 计算所有商户两两之间的距离统计，而不是简单的顺序距离
     */
    private DistanceStatistics calculateDistanceStatistics(List<MerchantInfo> merchants) {
        if (merchants.size() < 2) {
            return new DistanceStatistics(0.0, 0.0, 0.0, 0.0);
        }

        List<Double> distances = new ArrayList<>();

        // 计算所有商户两两之间的距离
        for (int i = 0; i < merchants.size(); i++) {
            for (int j = i + 1; j < merchants.size(); j++) {
                MerchantInfo merchant1 = merchants.get(i);
                MerchantInfo merchant2 = merchants.get(j);

                if (GeoUtils.hasValidCoordinates(merchant1) && GeoUtils.hasValidCoordinates(merchant2)) {
                    double distance = GeoUtils.calculateDistance(
                        merchant1.getLatitude(), merchant1.getLongitude(),
                        merchant2.getLatitude(), merchant2.getLongitude()
                    );
                    distances.add(distance);
                }
            }
        }

        if (distances.isEmpty()) {
            return new DistanceStatistics(0.0, 0.0, 0.0, 0.0);
        }

        // 计算统计信息
        double sum = distances.stream().mapToDouble(Double::doubleValue).sum();
        double average = sum / distances.size();
        double max = distances.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
        double min = distances.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);

        return new DistanceStatistics(average, max, min, sum);
    }

    /**
     * 距离统计信息类
     */
    public static class DistanceStatistics {
        private final double averageDistance;
        private final double maxDistance;
        private final double minDistance;
        private final double totalDistance;

        public DistanceStatistics(double averageDistance, double maxDistance, double minDistance, double totalDistance) {
            this.averageDistance = averageDistance;
            this.maxDistance = maxDistance;
            this.minDistance = minDistance;
            this.totalDistance = totalDistance;
        }

        public double getAverageDistance() {
            return averageDistance;
        }

        public double getMaxDistance() {
            return maxDistance;
        }

        public double getMinDistance() {
            return minDistance;
        }

        public double getTotalDistance() {
            return totalDistance;
        }
    }
    
    /**
     * 解决方案类
     */
    public static class Solution {
        private List<MerchantInfo> merchants;
        private double totalDistance;
        private Map<MerchantCategory, Integer> actualSelection;
        private DistanceStatistics distanceStatistics;
        private Map<MerchantInfo, MerchantCategory> merchantClassifications;

        // Getters and Setters
        public List<MerchantInfo> getMerchants() {
            return merchants;
        }

        public void setMerchants(List<MerchantInfo> merchants) {
            this.merchants = merchants;
        }

        public double getTotalDistance() {
            return totalDistance;
        }

        public void setTotalDistance(double totalDistance) {
            this.totalDistance = totalDistance;
        }

        public Map<MerchantCategory, Integer> getActualSelection() {
            return actualSelection;
        }

        public void setActualSelection(Map<MerchantCategory, Integer> actualSelection) {
            this.actualSelection = actualSelection;
        }

        public DistanceStatistics getDistanceStatistics() {
            return distanceStatistics;
        }

        public void setDistanceStatistics(DistanceStatistics distanceStatistics) {
            this.distanceStatistics = distanceStatistics;
        }

        public Map<MerchantInfo, MerchantCategory> getMerchantClassifications() {
            return merchantClassifications;
        }

        public void setMerchantClassifications(Map<MerchantInfo, MerchantCategory> merchantClassifications) {
            this.merchantClassifications = merchantClassifications;
        }
    }
}
