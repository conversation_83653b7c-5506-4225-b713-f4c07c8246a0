package com.hz.merchant.service.impl;

import com.hz.merchant.config.MerchantSelectionProperties;
import com.hz.merchant.constant.MerchantConstants;
import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.domain.MerchantSelectionRequest;
import com.hz.merchant.domain.MerchantSelectionResponse;
import com.hz.merchant.mapper.MerchantInfoMapper;
import com.hz.merchant.service.IMerchantSelectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商户选择服务实现类 - 优化版本
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
public class MerchantSelectionServiceImpl implements IMerchantSelectionService {
    
    private static final Logger logger = LoggerFactory.getLogger(MerchantSelectionServiceImpl.class);
    
    @Autowired
    private MerchantInfoMapper merchantInfoMapper;
    
    @Autowired
    private MerchantSelectionProperties properties;

    @Autowired
    private NewIntelligentMerchantSelector newIntelligentMerchantSelector;

    @Autowired
    private ThreeDimensionalMerchantClassifier classifier;
    

    
    @Override
    public List<MerchantInfo> selectMerchantsByConditions(Long deptId, String chengxindengji, String zhongduancengji, String jingyingguimo, Integer num) {
        MerchantInfo query = new MerchantInfo();
        query.setCountyId(deptId);
        query.setChengxindengji(chengxindengji);
        query.setZhongduancengji(zhongduancengji);
        query.setJingyingguimo(jingyingguimo);
        if (num != null) {
            query.getParams().put("limit", num);
        }
        return merchantInfoMapper.selectMerchantInfoList(query);
    }
    
    private MerchantSelectionResponse.MerchantSelectionStatistics calculateStatistics(List<MerchantInfo> merchants) {
        return calculateStatistics(merchants, null);
    }

    private MerchantSelectionResponse.MerchantSelectionStatistics calculateStatistics(List<MerchantInfo> merchants,
                                                                                     Map<MerchantInfo, MultiDimensionalQuotaCalculator.MerchantCategory> cachedClassifications) {
        MerchantSelectionResponse.MerchantSelectionStatistics stats = new MerchantSelectionResponse.MerchantSelectionStatistics();
        if (merchants == null || merchants.isEmpty()) {
            return stats;
        }

        stats.setTotalCount(merchants.size());
        stats.setChengxindengjiCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getChengxindengji, Collectors.counting())));
        stats.setZhongduancengjiCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getZhongduancengji, Collectors.counting())));
        stats.setJingyingguimoCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getJingyingguimo, Collectors.counting())));
        stats.setYetaiCounts(merchants.stream().collect(Collectors.groupingBy(MerchantInfo::getYetai, Collectors.counting())));

        // 添加12类别统计 - 使用缓存的分类结果避免重复计算
        Map<String, Integer> categoryDistribution = new HashMap<>();
        for (MerchantInfo merchant : merchants) {
            try {
                MultiDimensionalQuotaCalculator.MerchantCategory category;
                if (cachedClassifications != null && cachedClassifications.containsKey(merchant)) {
                    // 使用缓存的分类结果
                    category = cachedClassifications.get(merchant);
                } else {
                    // 重新分类
                    category = classifier.classifyMerchant(merchant);
                }
                String categoryName = category.getDescription();
                categoryDistribution.put(categoryName, categoryDistribution.getOrDefault(categoryName, 0) + 1);
            } catch (IllegalArgumentException e) {
                logger.warn("商户分类参数错误: 商户[{}], 错误: {}", merchant.getMerchantName(), e.getMessage());
            } catch (Exception e) {
                logger.error("商户分类异常: 商户[{}], 错误: {}", merchant.getMerchantName(), e.getMessage(), e);
            }
        }
        stats.setCategoryDistribution(categoryDistribution);

        return stats;
    }
    
    private List<MerchantInfo> getAllMerchantsWithCoordinates(Map<String, Object> params) {
        logger.debug("从数据库获取具有有效坐标的商户信息，参数: {}", params);
        MerchantInfo query = new MerchantInfo();
        if (params != null) {
            if (params.get("deptId") != null) query.setCountyId((Long) params.get("deptId"));
            if (params.get("chengxindengji") != null) query.setChengxindengji((String) params.get("chengxindengji"));
            if (params.get("zhongduancengji") != null) query.setZhongduancengji((String) params.get("zhongduancengji"));
            if (params.get("jingyingguimo") != null) query.setJingyingguimo((String) params.get("jingyingguimo"));
        }
        // 使用优化的查询方法，在SQL层面过滤有效坐标，提高性能
        return merchantInfoMapper.selectMerchantInfoListWithValidCoordinates(query);
    }


    
    @Override
    public MerchantSelectionResponse selectMerchants(MerchantSelectionRequest request) {
        if (isConditionalSelection(request)) {
            // 条件抽取
            return performConditionalSelection(request);
        } else {
            // 智能抽取
            return performIntelligentSelection(request);
        }
    }
    
    private boolean isConditionalSelection(MerchantSelectionRequest request) {
        return request.getNum() != null && request.getNum() > 0;
    }
    
    private MerchantSelectionResponse performConditionalSelection(MerchantSelectionRequest request) {
        List<MerchantInfo> selectedMerchants = selectMerchantsByConditions(
                request.getDeptId(),
                request.getChengxindengji(),
                request.getZhongduancengji(),
                request.getJingyingguimo(),
                request.getNum()
        );
        
        if (selectedMerchants != null && !selectedMerchants.isEmpty()) {
            String message = MerchantConstants.MSG_CONDITIONAL_SUCCESS + "，共抽取到" + selectedMerchants.size() + "个商户";
            MerchantSelectionResponse response = new MerchantSelectionResponse(true, selectedMerchants, message);
            response.setStatistics(calculateStatistics(selectedMerchants));
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        } else {
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, List.of(), MerchantConstants.MSG_NO_MERCHANTS_FOUND);
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        }
    }
    
    private MerchantSelectionResponse performIntelligentSelection(MerchantSelectionRequest request) {
        try {
            logger.info("开始智能抽取商户...");

            // 1. 获取所有商户数据
            Map<String, Object> params = buildSelectionParams(request);
            List<MerchantInfo> allMerchants = getAllMerchantsWithCoordinates(params);

            if (allMerchants.isEmpty()) {
                MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), "没有找到符合条件的商户");
                response.setTotalCountConfig(properties.getTotalCount());
                return response;
            }

            // 2. 委托给新的智能选择器
            NewIntelligentMerchantSelector.Solution bestSolution = newIntelligentMerchantSelector.selectMerchants(allMerchants, properties);

            // 3. 处理结果
            if (bestSolution != null) {
                MerchantSelectionResponse response = new MerchantSelectionResponse(true, bestSolution.getMerchants(), MerchantConstants.MSG_INTELLIGENT_SUCCESS);
                response.setTotalDistance(bestSolution.getTotalDistance());
                // 使用缓存的分类结果计算统计信息，避免重复分类计算
                response.setStatistics(calculateStatistics(bestSolution.getMerchants(), bestSolution.getMerchantClassifications()));
                response.setTotalCountConfig(properties.getTotalCount());
                return response;
            }

            String message = MerchantConstants.MSG_INSUFFICIENT_QUOTA + properties.getTotalCount() + "个商户组合";
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), message);
            response.setTotalCountConfig(properties.getTotalCount());
            return response;

        } catch (DataAccessException e) {
            logger.error("数据库访问异常", e);
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), MerchantConstants.ERROR_DATABASE_ACCESS);
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        } catch (IllegalArgumentException e) {
            logger.error("参数验证异常", e);
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), "参数错误: " + e.getMessage());
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        } catch (Exception e) {
            logger.error("智能抽取商户时发生未知异常", e);
            MerchantSelectionResponse response = new MerchantSelectionResponse(false, new ArrayList<>(), MerchantConstants.ERROR_INTELLIGENT_SELECTION + ": " + e.getMessage());
            response.setTotalCountConfig(properties.getTotalCount());
            return response;
        }
    }
    
    private Map<String, Object> buildSelectionParams(MerchantSelectionRequest request) {
        Map<String, Object> params = new HashMap<>();
        if (request.getDeptId() != null) {
            params.put("deptId", request.getDeptId());
        }
        if (request.getChengxindengji() != null) {
            params.put("chengxindengji", request.getChengxindengji());
        }
        if (request.getZhongduancengji() != null) {
            params.put("zhongduancengji", request.getZhongduancengji());
        }
        if (request.getJingyingguimo() != null) {
            params.put("jingyingguimo", request.getJingyingguimo());
        }
        return params;
    }
} 