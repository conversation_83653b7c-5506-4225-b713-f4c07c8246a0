package com.hz.merchant.domain;

/**
 * 商户抽取请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
public class MerchantSelectionRequest {
    
    /** 地区ID */
    private Long deptId;
    
    /** 诚信等级 */
    private String chengxindengji;
    
    /** 终端类型 */
    private String zhongduancengji;
    
    /** 经营规模 */
    private String jingyingguimo;
    
    /** 抽取数量 */
    private Integer num;
    
    /** 抽取类型：smart-智能抽取，condition-条件抽取 */
    private String selectionType;
    
    public Long getDeptId() {
        return deptId;
    }
    
    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }
    
    public String getChengxindengji() {
        return chengxindengji;
    }
    
    public void setChengxindengji(String chengxindengji) {
        this.chengxindengji = chengxindengji;
    }
    
    public String getZhongduancengji() {
        return zhongduancengji;
    }
    
    public void setZhongduancengji(String zhongduancengji) {
        this.zhongduancengji = zhongduancengji;
    }
    
    public String getJingyingguimo() {
        return jingyingguimo;
    }
    
    public void setJingyingguimo(String jingyingguimo) {
        this.jingyingguimo = jingyingguimo;
    }
    
    public Integer getNum() {
        return num;
    }
    
    public void setNum(Integer num) {
        this.num = num;
    }
    
    public String getSelectionType() {
        return selectionType;
    }
    
    public void setSelectionType(String selectionType) {
        this.selectionType = selectionType;
    }
    
    @Override
    public String toString() {
        return "MerchantSelectionRequest{" +
                "deptId=" + deptId +
                ", chengxindengji='" + chengxindengji + '\'' +
                ", zhongduancengji='" + zhongduancengji + '\'' +
                ", jingyingguimo='" + jingyingguimo + '\'' +
                ", num=" + num +
                ", selectionType='" + selectionType + '\'' +
                '}';
    }
} 