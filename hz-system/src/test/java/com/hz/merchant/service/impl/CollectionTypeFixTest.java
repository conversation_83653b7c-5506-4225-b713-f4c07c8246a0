package com.hz.merchant.service.impl;

import com.hz.merchant.domain.MerchantInfo;
import com.hz.merchant.constant.MerchantConstants;

/**
 * 测试采集类型字段修复
 */
public class CollectionTypeFixTest {
    
    public static void main(String[] args) {
        System.out.println("=== 测试采集类型字段修复 ===");
        
        // 测试1: 验证MerchantInfo类有collectionType字段的getter/setter
        testMerchantInfoCollectionType();
        
        // 测试2: 验证ThreeDimensionalMerchantClassifier的采集类型判断逻辑
        testCollectionTypeClassification();
        
        System.out.println("=== 所有测试通过！采集类型字段修复成功 ===");
    }
    
    private static void testMerchantInfoCollectionType() {
        System.out.println("测试1: MerchantInfo采集类型字段");
        
        MerchantInfo merchant = new MerchantInfo();
        
        // 测试设置采集类型
        merchant.setCollectionType(MerchantConstants.COLLECTION_TYPE_YES);
        Integer collectionType = merchant.getCollectionType();
        
        if (collectionType != null && collectionType.equals(MerchantConstants.COLLECTION_TYPE_YES)) {
            System.out.println("✓ MerchantInfo.collectionType字段正常工作");
        } else {
            throw new RuntimeException("✗ MerchantInfo.collectionType字段异常");
        }
        
        // 测试null值
        merchant.setCollectionType(null);
        collectionType = merchant.getCollectionType();
        
        if (collectionType == null) {
            System.out.println("✓ MerchantInfo.collectionType支持null值");
        } else {
            throw new RuntimeException("✗ MerchantInfo.collectionType不支持null值");
        }
    }
    
    private static void testCollectionTypeClassification() {
        System.out.println("测试2: 采集类型分类逻辑");
        
        ThreeDimensionalMerchantClassifier classifier = new ThreeDimensionalMerchantClassifier();
        
        // 测试采集商户
        MerchantInfo collectionMerchant = createTestMerchant("测试采集商户", MerchantConstants.COLLECTION_TYPE_YES);
        String details1 = classifier.getClassificationDetails(collectionMerchant);
        
        if (details1.contains("采集类型=采集")) {
            System.out.println("✓ 采集商户分类正确: " + details1);
        } else {
            throw new RuntimeException("✗ 采集商户分类错误: " + details1);
        }
        
        // 测试非采集商户
        MerchantInfo nonCollectionMerchant = createTestMerchant("测试非采集商户", MerchantConstants.COLLECTION_TYPE_NO);
        String details2 = classifier.getClassificationDetails(nonCollectionMerchant);
        
        if (details2.contains("采集类型=非采集")) {
            System.out.println("✓ 非采集商户分类正确: " + details2);
        } else {
            throw new RuntimeException("✗ 非采集商户分类错误: " + details2);
        }
        
        // 测试null值商户（应该被视为非采集）
        MerchantInfo nullCollectionMerchant = createTestMerchant("测试null商户", null);
        String details3 = classifier.getClassificationDetails(nullCollectionMerchant);
        
        if (details3.contains("采集类型=非采集")) {
            System.out.println("✓ null采集类型商户分类正确: " + details3);
        } else {
            throw new RuntimeException("✗ null采集类型商户分类错误: " + details3);
        }
    }
    
    private static MerchantInfo createTestMerchant(String name, Integer collectionType) {
        MerchantInfo merchant = new MerchantInfo();
        merchant.setMerchantName(name);
        merchant.setCollectionType(collectionType);
        merchant.setMarketType("城镇"); // 默认城镇
        merchant.setZhongduancengji("普通"); // 默认普通终端
        return merchant;
    }
}
