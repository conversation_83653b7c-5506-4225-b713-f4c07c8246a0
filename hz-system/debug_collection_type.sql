-- 查看 collectionType 字段的实际数据分布
SELECT 
    collection_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM merchant_info), 2) as percentage
FROM merchant_info 
WHERE collection_type IS NOT NULL
GROUP BY collection_type
ORDER BY count DESC;

-- 查看前10条记录的详细信息
SELECT 
    id,
    merchant_name,
    collection_type,
    market_type,
    zhongduancengji,
    yetai
FROM merchant_info 
LIMIT 10;

-- 查看是否有采集类型为1的记录
SELECT COUNT(*) as collection_count
FROM merchant_info 
WHERE collection_type = '1';

-- 查看是否有采集类型包含"采集"关键字的记录
SELECT COUNT(*) as collection_keyword_count
FROM merchant_info 
WHERE collection_type LIKE '%采集%';
