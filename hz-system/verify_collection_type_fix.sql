-- 验证采集类型字段修复的SQL查询
-- 这些查询对应MyBatis mapper中修复的SQL语句

-- 1. 验证selectMerchantInfoVo片段（对应mapper中的SQL片段）
-- 这个查询应该包含collection_type字段
SELECT 
    mi.id, 
    mi.merchant_name, 
    mi.market_type, 
    mi.zhongduancengji, 
    mi.collection_type,  -- 这是我们修复的字段
    mi.create_time
FROM merchant_info mi 
WHERE mi.del_flag = '0' 
LIMIT 5;

-- 2. 验证selectMerchantInfoList查询（对应mapper中的主要查询方法）
-- 这个查询应该返回包含collection_type的完整商户信息
SELECT 
    mi.id, 
    mi.merchant_name, 
    mi.market_type, 
    mi.zhongduancengji, 
    mi.collection_type,  -- 修复的字段
    mi.create_time,
    mi.update_time
FROM merchant_info mi 
WHERE mi.del_flag = '0'
ORDER BY mi.create_time DESC 
LIMIT 10;

-- 3. 统计采集类型分布（验证数据完整性）
SELECT 
    CASE 
        WHEN mi.collection_type = 1 THEN '采集商户'
        WHEN mi.collection_type = 0 THEN '非采集商户'
        WHEN mi.collection_type IS NULL THEN 'NULL值商户'
        ELSE '其他值商户'
    END AS collection_type_desc,
    COUNT(*) as merchant_count
FROM merchant_info mi 
WHERE mi.del_flag = '0'
GROUP BY mi.collection_type
ORDER BY mi.collection_type;

-- 4. 验证三维分类所需的所有字段都存在
SELECT 
    mi.market_type as 地域类型,
    mi.zhongduancengji as 终端类型,
    CASE 
        WHEN mi.collection_type = 1 THEN '采集'
        ELSE '非采集'
    END as 采集类型,
    COUNT(*) as 商户数量
FROM merchant_info mi 
WHERE mi.del_flag = '0'
  AND mi.market_type IS NOT NULL 
  AND mi.zhongduancengji IS NOT NULL
GROUP BY mi.market_type, mi.zhongduancengji, mi.collection_type
ORDER BY mi.market_type, mi.zhongduancengji, mi.collection_type;

-- 5. 检查是否还有collection_type为NULL的商户（这是原问题的根源）
SELECT 
    COUNT(*) as null_collection_type_count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM merchant_info WHERE del_flag = '0') as null_percentage
FROM merchant_info mi 
WHERE mi.del_flag = '0' 
  AND mi.collection_type IS NULL;

-- 6. 验证INSERT语句会正确处理collection_type字段
-- （这个查询只是展示INSERT的结构，不会实际执行）
/*
INSERT INTO merchant_info (
    merchant_name, 
    market_type, 
    zhongduancengji, 
    collection_type,  -- 修复后应该包含这个字段
    create_time, 
    del_flag
) VALUES (
    '测试商户', 
    '城镇', 
    '普通', 
    1,  -- 采集类型
    NOW(), 
    '0'
);
*/

-- 7. 验证UPDATE语句会正确处理collection_type字段
-- （这个查询只是展示UPDATE的结构，不会实际执行）
/*
UPDATE merchant_info 
SET 
    merchant_name = '更新测试商户',
    market_type = '乡村',
    zhongduancengji = '现代',
    collection_type = 0,  -- 修复后应该能更新这个字段
    update_time = NOW()
WHERE id = 1 
  AND del_flag = '0';
*/
